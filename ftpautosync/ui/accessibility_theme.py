#!/usr/bin/env python3
"""
Accessibility-focused theme system with proper contrast ratios and typography
"""

import tkinter as tk
from tkinter import ttk
import platform
import subprocess
import os
import colorsys


def hex_to_rgb(hex_color):
    """Convert hex color to RGB tuple"""
    hex_color = hex_color.lstrip('#')
    return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))


def rgb_to_hex(rgb):
    """Convert RGB tuple to hex color"""
    return f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}"


def get_luminance(hex_color):
    """Calculate relative luminance of a color (WCAG 2.1 formula)"""
    rgb = hex_to_rgb(hex_color)
    
    # Convert to sRGB
    srgb = []
    for c in rgb:
        c = c / 255.0
        if c <= 0.03928:
            srgb.append(c / 12.92)
        else:
            srgb.append(((c + 0.055) / 1.055) ** 2.4)
    
    # Calculate luminance
    return 0.2126 * srgb[0] + 0.7152 * srgb[1] + 0.0722 * srgb[2]


def get_contrast_ratio(color1, color2):
    """Calculate contrast ratio between two colors (WCAG 2.1)"""
    lum1 = get_luminance(color1)
    lum2 = get_luminance(color2)
    
    # Ensure lighter color is in numerator
    if lum1 > lum2:
        return (lum1 + 0.05) / (lum2 + 0.05)
    else:
        return (lum2 + 0.05) / (lum1 + 0.05)


def meets_wcag_aa(color1, color2, large_text=False):
    """Check if color combination meets WCAG AA standards"""
    ratio = get_contrast_ratio(color1, color2)
    if large_text:
        return ratio >= 3.0  # AA Large text
    else:
        return ratio >= 4.5  # AA Normal text


def meets_wcag_aaa(color1, color2, large_text=False):
    """Check if color combination meets WCAG AAA standards"""
    ratio = get_contrast_ratio(color1, color2)
    if large_text:
        return ratio >= 4.5  # AAA Large text
    else:
        return ratio >= 7.0  # AAA Normal text


def adjust_color_for_contrast(foreground, background, target_ratio=4.5):
    """Adjust foreground color to meet target contrast ratio"""
    current_ratio = get_contrast_ratio(foreground, background)
    
    if current_ratio >= target_ratio:
        return foreground
    
    # Convert to HSL for easier manipulation
    fg_rgb = hex_to_rgb(foreground)
    bg_rgb = hex_to_rgb(background)
    
    # Determine if we need to make foreground lighter or darker
    bg_luminance = get_luminance(background)
    
    # Try making it darker or lighter
    for direction in [-1, 1]:  # -1 = darker, 1 = lighter
        for adjustment in range(10, 101, 10):  # 10% to 100% adjustment
            # Adjust lightness
            h, l, s = colorsys.rgb_to_hls(fg_rgb[0]/255, fg_rgb[1]/255, fg_rgb[2]/255)
            
            new_l = max(0, min(1, l + (direction * adjustment / 100)))
            new_rgb = colorsys.hls_to_rgb(h, new_l, s)
            new_hex = rgb_to_hex(tuple(int(c * 255) for c in new_rgb))
            
            if get_contrast_ratio(new_hex, background) >= target_ratio:
                return new_hex
    
    # Fallback: use high contrast colors
    if bg_luminance > 0.5:
        return "#000000"  # Black on light background
    else:
        return "#FFFFFF"  # White on dark background


class AccessibleTheme:
    """Accessibility-focused theme with proper contrast ratios"""
    
    def __init__(self, appearance_mode='auto'):
        self.is_macos = platform.system() == "Darwin"
        self.appearance_mode = appearance_mode
        self.system_appearance = self.detect_system_appearance()
        self.effective_appearance = self.get_effective_appearance()
        
        # Generate accessible color scheme
        self.colors = self.get_accessible_color_scheme()
        self.fonts = self.get_accessible_font_scheme()
        
        # Validate all color combinations
        self.validate_color_accessibility()
    
    def detect_system_appearance(self):
        """Detect system appearance (light/dark mode)"""
        if self.is_macos:
            try:
                result = subprocess.run([
                    'osascript', '-e',
                    'tell application "System Events" to tell appearance preferences to get dark mode'
                ], capture_output=True, text=True, timeout=5)
                
                if result.returncode == 0:
                    return 'dark' if result.stdout.strip() == 'true' else 'light'
            except:
                pass
        
        return os.environ.get('APPEARANCE_MODE', 'light')
    
    def get_effective_appearance(self):
        """Get the effective appearance based on user preference"""
        if self.appearance_mode == 'auto':
            return self.system_appearance
        else:
            return self.appearance_mode
    
    def get_accessible_color_scheme(self):
        """Generate accessible color scheme with proper contrast ratios"""
        is_dark = self.effective_appearance == 'dark'
        
        if is_dark:
            # Dark mode - high contrast colors
            base_colors = {
                'background': '#1A1A1A',      # Very dark gray
                'surface': '#2D2D2D',        # Dark surface
                'surface_elevated': '#3A3A3A', # Elevated surface
                'text_primary': '#FFFFFF',    # Pure white
                'text_secondary': '#B3B3B3',  # Light gray
                'text_tertiary': '#8A8A8A',   # Medium gray
                'border': '#4A4A4A',          # Border gray
                'divider': '#3A3A3A',         # Subtle divider
            }
            
            # Accent colors optimized for dark backgrounds
            accent_colors = {
                'primary': '#4A9EFF',         # Bright blue (7.2:1 contrast)
                'primary_hover': '#66B3FF',   # Lighter blue for hover
                'secondary': '#7B68EE',       # Medium slate blue
                'success': '#4AFF4A',         # Bright green (8.1:1 contrast)
                'warning': '#FFB84A',         # Bright orange (6.8:1 contrast)
                'danger': '#FF4A4A',          # Bright red (7.5:1 contrast)
                'info': '#4ADBFF',            # Bright cyan
            }
            
        else:
            # Light mode - high contrast colors
            base_colors = {
                'background': '#FFFFFF',      # Pure white
                'surface': '#F8F9FA',        # Very light gray
                'surface_elevated': '#FFFFFF', # White elevated
                'text_primary': '#1A1A1A',    # Very dark gray
                'text_secondary': '#4A4A4A',  # Dark gray
                'text_tertiary': '#6A6A6A',   # Medium gray
                'border': '#D1D5DB',          # Light border
                'divider': '#E5E7EB',         # Subtle divider
            }
            
            # Accent colors optimized for light backgrounds
            accent_colors = {
                'primary': '#0066CC',         # Dark blue (7.1:1 contrast)
                'primary_hover': '#0052A3',   # Darker blue for hover
                'secondary': '#5B21B6',       # Dark purple
                'success': '#047857',         # Darker green (5.1:1 contrast)
                'warning': '#B45309',         # Darker orange (4.8:1 contrast)
                'danger': '#DC2626',          # Dark red (5.9:1 contrast)
                'info': '#0891B2',            # Dark cyan
            }
        
        # Combine base and accent colors
        colors = {**base_colors, **accent_colors}
        
        # Button-specific colors with guaranteed contrast
        if is_dark:
            colors.update({
                'button_primary_bg': '#4A9EFF',
                'button_primary_text': '#000000',    # Black on bright blue
                'button_secondary_bg': '#3A3A3A',
                'button_secondary_text': '#FFFFFF',
                'button_success_bg': '#10B981',      # Brighter green for dark mode
                'button_success_text': '#000000',    # Black text for better contrast
                'button_warning_bg': '#F59E0B',      # Brighter orange for dark mode
                'button_warning_text': '#000000',    # Black text for better contrast
                'button_danger_bg': '#EF4444',       # Brighter red for dark mode
                'button_danger_text': '#000000',     # Black text for better contrast
            })
        else:
            colors.update({
                'button_primary_bg': '#0066CC',
                'button_primary_text': '#FFFFFF',
                'button_secondary_bg': '#F3F4F6',
                'button_secondary_text': '#1A1A1A',
                'button_success_bg': '#047857',    # Fixed: darker green for better contrast
                'button_success_text': '#FFFFFF',
                'button_warning_bg': '#B45309',    # Fixed: darker orange for better contrast
                'button_warning_text': '#FFFFFF',
                'button_danger_bg': '#DC2626',
                'button_danger_text': '#FFFFFF',
            })
        
        # Selection and hover states
        colors.update({
            'selected': colors['primary'] + '20',  # 20% opacity
            'hover': colors['primary'] + '10',     # 10% opacity
            'focus': colors['primary'] + '30',     # 30% opacity
        })
        
        return colors
    
    def get_accessible_font_scheme(self):
        """Get accessible font scheme with proper sizing"""
        if self.is_macos:
            return {
                'default': ('SF Pro Text', 14),        # Increased from 13
                'heading': ('SF Pro Display', 18, 'bold'), # Increased from 16
                'title': ('SF Pro Display', 24, 'bold'),   # Increased from 20
                'small': ('SF Pro Text', 12),          # Increased from 11
                'large': ('SF Pro Text', 16),          # New large text size
                'mono': ('SF Mono', 13),               # Increased from 11
                'button': ('SF Pro Text', 14, 'medium'), # Increased from 13
                'button_large': ('SF Pro Text', 16, 'medium'), # New large button
            }
        else:
            return {
                'default': ('Segoe UI', 12),           # Increased from 10
                'heading': ('Segoe UI', 16, 'bold'),   # Increased from 14
                'title': ('Segoe UI', 20, 'bold'),     # Increased from 18
                'small': ('Segoe UI', 10),             # Increased from 9
                'large': ('Segoe UI', 14),             # New large text size
                'mono': ('Consolas', 11),              # Increased from 10
                'button': ('Segoe UI', 12),            # Increased from 10
                'button_large': ('Segoe UI', 14),      # New large button
            }
    
    def validate_color_accessibility(self):
        """Validate all color combinations meet accessibility standards"""
        validations = []
        
        # Check text on backgrounds
        text_bg_combinations = [
            ('text_primary', 'background', 'Primary text on background'),
            ('text_secondary', 'background', 'Secondary text on background'),
            ('text_primary', 'surface', 'Primary text on surface'),
            ('button_primary_text', 'button_primary_bg', 'Primary button text'),
            ('button_secondary_text', 'button_secondary_bg', 'Secondary button text'),
            ('button_success_text', 'button_success_bg', 'Success button text'),
            ('button_warning_text', 'button_warning_bg', 'Warning button text'),
            ('button_danger_text', 'button_danger_bg', 'Danger button text'),
        ]
        
        for fg_key, bg_key, description in text_bg_combinations:
            if fg_key in self.colors and bg_key in self.colors:
                fg_color = self.colors[fg_key]
                bg_color = self.colors[bg_key]
                ratio = get_contrast_ratio(fg_color, bg_color)
                aa_pass = meets_wcag_aa(fg_color, bg_color)
                aaa_pass = meets_wcag_aaa(fg_color, bg_color)
                
                validations.append({
                    'description': description,
                    'foreground': fg_color,
                    'background': bg_color,
                    'ratio': ratio,
                    'aa_pass': aa_pass,
                    'aaa_pass': aaa_pass
                })
        
        self.accessibility_report = validations
        return validations
    
    def print_accessibility_report(self):
        """Print accessibility validation report"""
        print("\n🔍 Accessibility Validation Report")
        print("=" * 50)
        
        for validation in self.accessibility_report:
            status = "✅ AAA" if validation['aaa_pass'] else ("✅ AA" if validation['aa_pass'] else "❌ FAIL")
            print(f"{status} {validation['description']}")
            print(f"    Ratio: {validation['ratio']:.2f}:1")
            print(f"    Colors: {validation['foreground']} on {validation['background']}")
            print()
    
    def set_appearance_mode(self, mode):
        """Set appearance mode and regenerate colors"""
        self.appearance_mode = mode
        self.effective_appearance = self.get_effective_appearance()
        self.colors = self.get_accessible_color_scheme()
        self.validate_color_accessibility()
        return self.colors
