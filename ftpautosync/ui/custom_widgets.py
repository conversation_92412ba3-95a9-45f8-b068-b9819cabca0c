#!/usr/bin/env python3
"""
Custom widgets with forced visibility and icons
"""

import tkinter as tk
from tkinter import ttk
from typing import Optional, Callable


class AccessibleButton(tk.Button):
    """Accessible button with proper contrast ratios and typography"""

    def __init__(self, parent, text="", icon="", command=None, style_type="default",
                 large=False, **kwargs):
        # Get accessible theme - use global theme instance
        try:
            from .modern_theme import get_theme
            theme = get_theme()
            colors = theme.colors
            fonts = theme.fonts
        except:
            # Fallback colors with high contrast
            colors = self._get_fallback_colors()
            fonts = self._get_fallback_fonts()

        # Combine icon and text with proper spacing
        if icon and text:
            display_text = f"{icon}  {text}"  # Two spaces for better readability
        elif icon:
            display_text = icon
        else:
            display_text = text

        # Get accessible colors based on style type
        button_colors = self._get_button_colors(style_type, colors)

        # Choose appropriate font
        font_key = 'button_large' if large else 'button'
        button_font = fonts.get(font_key, ('SF Pro Text', 14))

        # Enhanced button configuration with accessibility features
        button_config = {
            'text': display_text,
            'command': command,
            'bg': button_colors['bg'],
            'fg': button_colors['fg'],
            'activebackground': button_colors['active_bg'],
            'activeforeground': button_colors['active_fg'],
            'disabledforeground': button_colors['disabled_fg'],
            'font': button_font,
            'relief': 'solid',
            'borderwidth': 2,
            'padx': 20 if large else 16,
            'pady': 12 if large else 10,
            'cursor': 'hand2',
            'takefocus': True,  # Keyboard accessibility
            'highlightthickness': 2,
            'highlightcolor': button_colors['focus'],
            'highlightbackground': button_colors['bg'],
        }

        # Override with any provided kwargs
        button_config.update(kwargs)

        super().__init__(parent, **button_config)

        # Add keyboard bindings for accessibility
        self.bind('<Return>', lambda e: self.invoke() if self['state'] != 'disabled' else None)
        self.bind('<space>', lambda e: self.invoke() if self['state'] != 'disabled' else None)

    def _get_fallback_colors(self):
        """Fallback high-contrast colors with guaranteed accessibility"""
        return {
            'background': '#FFFFFF',
            'text_primary': '#000000',
            'button_primary_bg': '#0066CC',      # 7.1:1 contrast ratio
            'button_primary_text': '#FFFFFF',
            'button_secondary_bg': '#F3F4F6',    # Light gray
            'button_secondary_text': '#1F2937',  # Dark gray, 8.5:1 contrast
            'button_success_bg': '#047857',      # Dark green, 5.1:1 contrast
            'button_success_text': '#FFFFFF',
            'button_warning_bg': '#B45309',      # Dark orange, 4.8:1 contrast
            'button_warning_text': '#FFFFFF',
            'button_danger_bg': '#DC2626',       # Red, 5.9:1 contrast
            'button_danger_text': '#FFFFFF',
            'primary': '#0066CC',
            'primary_hover': '#0052A3',
            'success': '#047857',
            'warning': '#B45309',
            'danger': '#DC2626',
            'surface_elevated': '#E5E7EB',
        }

    def _get_fallback_fonts(self):
        """Fallback font configuration"""
        return {
            'button': ('SF Pro Text', 14),
            'button_large': ('SF Pro Text', 16),
        }

    def _get_button_colors(self, style_type, colors):
        """Get button colors based on style type with proper contrast"""
        if style_type == "accent" or style_type == "primary":
            # Force high contrast colors for accent buttons
            bg_color = colors.get('button_primary_bg', '#0066CC')  # Strong blue
            fg_color = colors.get('button_primary_text', '#FFFFFF')  # White text
            return {
                'bg': bg_color,
                'fg': fg_color,
                'active_bg': colors.get('primary_hover', '#0052A3'),  # Darker blue
                'active_fg': fg_color,
                'disabled_fg': '#999999',
                'focus': colors.get('primary', '#0066CC'),
            }
        elif style_type == "success":
            # Force high contrast for success buttons
            bg_color = colors.get('button_success_bg', '#059669')  # Strong green
            fg_color = colors.get('button_success_text', '#FFFFFF')  # White text
            return {
                'bg': bg_color,
                'fg': fg_color,
                'active_bg': '#047857',  # Darker green
                'active_fg': fg_color,
                'disabled_fg': '#999999',
                'focus': colors.get('success', '#059669'),
            }
        elif style_type == "warning":
            # Force high contrast for warning buttons
            bg_color = colors.get('button_warning_bg', '#D97706')  # Strong orange
            fg_color = colors.get('button_warning_text', '#FFFFFF')  # White text
            return {
                'bg': bg_color,
                'fg': fg_color,
                'active_bg': '#B45309',  # Darker orange
                'active_fg': fg_color,
                'disabled_fg': '#999999',
                'focus': colors.get('warning', '#D97706'),
            }
        elif style_type == "danger":
            # Force high contrast for danger buttons
            bg_color = colors.get('button_danger_bg', '#DC2626')  # Strong red
            fg_color = colors.get('button_danger_text', '#FFFFFF')  # White text
            return {
                'bg': bg_color,
                'fg': fg_color,
                'active_bg': '#B91C1C',  # Darker red
                'active_fg': fg_color,
                'disabled_fg': '#999999',
                'focus': colors.get('danger', '#DC2626'),
            }
        else:  # default/secondary
            # Force high contrast for default buttons
            bg_color = colors.get('button_secondary_bg', '#F3F4F6')  # Light gray
            fg_color = colors.get('button_secondary_text', '#1F2937')  # Dark gray
            return {
                'bg': bg_color,
                'fg': fg_color,
                'active_bg': colors.get('surface_elevated', '#E5E7EB'),
                'active_fg': fg_color,
                'disabled_fg': '#999999',
                'focus': colors.get('primary', '#0066CC'),
            }


# Alias for backward compatibility
VisibleButton = AccessibleButton


class IconButton(VisibleButton):
    """Button with prominent icon"""
    
    def __init__(self, parent, icon="", text="", command=None, **kwargs):
        super().__init__(parent, text=text, icon=icon, command=command, **kwargs)


class ModernToolbar(ttk.Frame):
    """Modern toolbar with visible buttons"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        self.buttons = []
    
    def add_button(self, text="", icon="", command=None, style_type="default", separator=False):
        """Add a button to the toolbar"""
        if separator and self.buttons:
            sep = ttk.Separator(self, orient='vertical')
            sep.pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        btn = VisibleButton(self, text=text, icon=icon, command=command, style_type=style_type)
        btn.pack(side=tk.LEFT, padx=2)
        self.buttons.append(btn)
        return btn
    
    def add_space(self):
        """Add spacing between button groups"""
        spacer = ttk.Frame(self, width=20)
        spacer.pack(side=tk.LEFT)


class ModernButtonFrame(ttk.Frame):
    """Frame with modern visible buttons"""
    
    def __init__(self, parent, title="", **kwargs):
        super().__init__(parent, **kwargs)
        
        if title:
            self.title_label = ttk.Label(self, text=title, font=('SF Pro Display', 12, 'bold'))
            self.title_label.pack(anchor=tk.W, pady=(0, 10))
        
        self.button_frame = ttk.Frame(self)
        self.button_frame.pack(fill=tk.X)
    
    def add_button(self, text="", icon="", command=None, style_type="default", side=tk.LEFT):
        """Add a visible button"""
        btn = VisibleButton(self.button_frame, text=text, icon=icon, command=command, style_type=style_type)
        btn.pack(side=side, padx=5, pady=2)
        return btn


# Icon constants for consistency
ICONS = {
    # File operations
    'upload': '📤',
    'download': '📥',
    'sync': '🔄',
    'folder': '📁',
    'file': '📄',
    'open': '📂',
    
    # Actions
    'add': '➕',
    'edit': '✏️',
    'delete': '🗑️',
    'start': '▶️',
    'stop': '⏹️',
    'pause': '⏸️',
    'resume': '▶️',
    'refresh': '🔄',
    'save': '💾',
    'cancel': '❌',
    'ok': '✅',
    
    # Navigation
    'back': '⬅️',
    'forward': '➡️',
    'up': '⬆️',
    'down': '⬇️',
    'home': '🏠',
    
    # Tools
    'settings': '⚙️',
    'preferences': '🔧',
    'info': 'ℹ️',
    'help': '❓',
    'search': '🔍',
    
    # Status
    'success': '✅',
    'warning': '⚠️',
    'error': '❌',
    'info_status': 'ℹ️',
    'loading': '⏳',
    
    # Network
    'connected': '🟢',
    'disconnected': '🔴',
    'connecting': '🟡',
    
    # UI
    'menu': '☰',
    'close': '✖️',
    'minimize': '➖',
    'maximize': '⬜',
    'window': '🗂️',
    'transfer': '🚀',
    'appearance': '🎨',
    
    # Protocols
    'ftp': '📁',
    'ftps': '🔒',
    'sftp': '🔐',
    'scp': '🛡️',
}


def create_icon_button(parent, icon_key, text="", command=None, style_type="default", **kwargs):
    """Create a button with a predefined icon"""
    icon = ICONS.get(icon_key, "")
    return VisibleButton(parent, text=text, icon=icon, command=command, style_type=style_type, **kwargs)


def create_toolbar_button(parent, icon_key, text="", command=None, **kwargs):
    """Create a toolbar-style button"""
    icon = ICONS.get(icon_key, "")
    return VisibleButton(parent, text=text, icon=icon, command=command, 
                        font=('SF Pro Display', 11), padx=10, pady=6, **kwargs)


class StatusIndicator(tk.Label):
    """Status indicator with icon and text"""
    
    def __init__(self, parent, **kwargs):
        default_config = {
            'font': ('SF Pro Display', 11),
            'bg': parent.cget('bg') if hasattr(parent, 'cget') else '#F0F0F0',
            'fg': '#000000'
        }
        default_config.update(kwargs)
        super().__init__(parent, **default_config)
    
    def set_status(self, status, text=""):
        """Set status with icon"""
        status_icons = {
            'connected': '🟢',
            'disconnected': '🔴',
            'connecting': '🟡',
            'syncing': '🔄',
            'idle': '💤',
            'error': '❌',
            'success': '✅',
            'warning': '⚠️'
        }
        
        icon = status_icons.get(status, '')
        display_text = f"{icon} {text}" if text else icon
        self.config(text=display_text)


class ProgressWindow:
    """Modern progress window with visible controls"""
    
    def __init__(self, parent, title="Progress", width=600, height=400):
        self.parent = parent
        self.window = tk.Toplevel(parent)
        self.window.title(title)
        self.window.geometry(f"{width}x{height}")
        self.window.transient(parent)
        self.window.grab_set()
        
        # Center window
        self.center_window(width, height)
        
        # Create main frame
        self.main_frame = ttk.Frame(self.window, padding="20")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Header with title and controls
        self.create_header()
        
        # Progress area
        self.create_progress_area()
        
        # Status bar
        self.create_status_bar()
    
    def center_window(self, width, height):
        """Center the window on parent"""
        self.window.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (width // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_header(self):
        """Create header with title and controls"""
        header_frame = ttk.Frame(self.main_frame)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Title
        self.title_label = ttk.Label(header_frame, text="🚀 Operation in Progress", 
                                    font=('SF Pro Display', 16, 'bold'))
        self.title_label.pack(side=tk.LEFT)
        
        # Control buttons
        controls_frame = ttk.Frame(header_frame)
        controls_frame.pack(side=tk.RIGHT)
        
        self.pause_btn = create_icon_button(controls_frame, 'pause', "Pause", style_type="warning")
        self.pause_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.cancel_btn = create_icon_button(controls_frame, 'cancel', "Cancel", style_type="danger")
        self.cancel_btn.pack(side=tk.LEFT)
    
    def create_progress_area(self):
        """Create progress display area"""
        progress_frame = ttk.LabelFrame(self.main_frame, text="📊 Progress", padding="15")
        progress_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # Overall progress
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, 
                                          mode='determinate', length=400)
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))
        
        # Progress text
        self.progress_text = tk.Label(progress_frame, text="Starting...", 
                                     font=('SF Pro Display', 12),
                                     bg='white', fg='black')
        self.progress_text.pack()
    
    def create_status_bar(self):
        """Create status bar"""
        status_frame = ttk.Frame(self.main_frame)
        status_frame.pack(fill=tk.X)
        
        self.status_indicator = StatusIndicator(status_frame)
        self.status_indicator.pack(side=tk.LEFT)
        self.status_indicator.set_status('connecting', 'Initializing...')
    
    def update_progress(self, value, text=""):
        """Update progress"""
        self.progress_var.set(value)
        if text:
            self.progress_text.config(text=text)
    
    def set_status(self, status, text=""):
        """Update status"""
        self.status_indicator.set_status(status, text)
    
    def close(self):
        """Close the window"""
        self.window.destroy()
