#!/usr/bin/env python3
"""
Modern dual-pane file browser similar to Transmit
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Callable
import threading
import queue


class FileItem:
    """Represents a file or directory item"""
    def __init__(self, name: str, path: str, is_dir: bool, size: int = 0, 
                 modified: datetime = None, permissions: str = "", sync_enabled: bool = True):
        self.name = name
        self.path = path
        self.is_dir = is_dir
        self.size = size
        self.modified = modified or datetime.now()
        self.permissions = permissions
        self.sync_enabled = sync_enabled
        self.is_selected = False


class ModernFileTree(ttk.Treeview):
    """Modern file tree with icons and context menus"""
    
    def __init__(self, parent, side="local", **kwargs):
        super().__init__(parent, **kwargs)
        self.side = side  # "local" or "remote"
        self.current_path = ""
        self.items = {}  # item_id -> FileItem mapping
        self.callbacks = {}
        
        self.setup_columns()
        self.setup_icons()
        self.setup_context_menu()
        self.setup_bindings()
    
    def setup_columns(self):
        """Setup tree columns"""
        self['columns'] = ('size', 'modified', 'permissions', 'sync')
        self['show'] = 'tree headings'
        
        # Configure columns
        self.heading('#0', text='📁 Name', anchor='w')
        self.heading('size', text='📏 Size', anchor='e')
        self.heading('modified', text='🕒 Modified', anchor='center')
        self.heading('permissions', text='🔒 Permissions', anchor='center')
        self.heading('sync', text='🔄 Sync', anchor='center')
        
        # Column widths
        self.column('#0', width=300, minwidth=200)
        self.column('size', width=100, minwidth=80)
        self.column('modified', width=150, minwidth=120)
        self.column('permissions', width=100, minwidth=80)
        self.column('sync', width=60, minwidth=50)
    
    def setup_icons(self):
        """Setup file type icons"""
        self.icons = {
            'folder': '📁',
            'folder_open': '📂',
            'file': '📄',
            'image': '🖼️',
            'code': '💻',
            'archive': '📦',
            'document': '📝',
            'music': '🎵',
            'video': '🎬',
            'up': '⬆️'
        }
    
    def get_file_icon(self, file_item: FileItem) -> str:
        """Get icon for file type"""
        if file_item.is_dir:
            return self.icons['folder']
        
        ext = Path(file_item.name).suffix.lower()
        
        if ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg']:
            return self.icons['image']
        elif ext in ['.py', '.js', '.html', '.css', '.php', '.cpp', '.java']:
            return self.icons['code']
        elif ext in ['.zip', '.tar', '.gz', '.rar', '.7z']:
            return self.icons['archive']
        elif ext in ['.txt', '.md', '.doc', '.docx', '.pdf']:
            return self.icons['document']
        elif ext in ['.mp3', '.wav', '.flac', '.aac']:
            return self.icons['music']
        elif ext in ['.mp4', '.avi', '.mkv', '.mov']:
            return self.icons['video']
        else:
            return self.icons['file']
    
    def setup_context_menu(self):
        """Setup right-click context menu"""
        self.context_menu = tk.Menu(self, tearoff=0)
        
        if self.side == "local":
            self.context_menu.add_command(label="📤 Upload", command=self.upload_selected)
            self.context_menu.add_command(label="📂 Open in Finder", command=self.open_in_finder)
            self.context_menu.add_separator()
            self.context_menu.add_command(label="✏️ Rename", command=self.rename_item)
            self.context_menu.add_command(label="🗑️ Delete", command=self.delete_item)
            self.context_menu.add_separator()
            self.context_menu.add_command(label="📋 Copy Path", command=self.copy_path)
            self.context_menu.add_separator()
        else:  # remote
            self.context_menu.add_command(label="📥 Download", command=self.download_selected)
            self.context_menu.add_command(label="👁️ View", command=self.view_remote_file)
            self.context_menu.add_separator()
            self.context_menu.add_command(label="✏️ Rename", command=self.rename_item)
            self.context_menu.add_command(label="🗑️ Delete", command=self.delete_item)
            self.context_menu.add_separator()
            self.context_menu.add_command(label="📋 Copy Path", command=self.copy_path)
            self.context_menu.add_separator()
        
        # Common options
        sync_menu = tk.Menu(self.context_menu, tearoff=0)
        sync_menu.add_command(label="✅ Enable Sync", command=lambda: self.toggle_sync(True))
        sync_menu.add_command(label="❌ Disable Sync", command=lambda: self.toggle_sync(False))
        sync_menu.add_command(label="🔄 Toggle Sync", command=lambda: self.toggle_sync(None))
        
        self.context_menu.add_cascade(label="🔄 Sync Options", menu=sync_menu)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="🔄 Refresh", command=self.refresh)
        self.context_menu.add_command(label="ℹ️ Properties", command=self.show_properties)
    
    def setup_bindings(self):
        """Setup event bindings"""
        self.bind('<Button-2>', self.show_context_menu)  # Right click
        self.bind('<Control-Button-1>', self.show_context_menu)  # Ctrl+click
        self.bind('<Double-1>', self.on_double_click)
        self.bind('<Return>', self.on_double_click)
        self.bind('<BackSpace>', self.go_up)
        self.bind('<F5>', lambda e: self.refresh())
    
    def show_context_menu(self, event):
        """Show context menu"""
        item = self.identify_row(event.y)
        if item:
            self.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)
    
    def on_double_click(self, event=None):
        """Handle double click"""
        selection = self.selection()
        if selection:
            item_id = selection[0]
            file_item = self.items.get(item_id)
            if file_item and file_item.is_dir:
                self.navigate_to(file_item.path)
    
    def format_size(self, size: int) -> str:
        """Format file size"""
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.1f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.1f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.1f} GB"
    
    def add_file_item(self, file_item: FileItem, parent=''):
        """Add file item to tree"""
        icon = self.get_file_icon(file_item)
        
        # Format values
        size_text = "" if file_item.is_dir else self.format_size(file_item.size)
        modified_text = file_item.modified.strftime('%m/%d/%y %H:%M')
        sync_text = "✅" if file_item.sync_enabled else "❌"
        
        # Insert item
        item_id = self.insert(parent, 'end', 
                             text=f"{icon} {file_item.name}",
                             values=(size_text, modified_text, file_item.permissions, sync_text))
        
        # Store reference
        self.items[item_id] = file_item
        
        # Add visual indicators
        if not file_item.sync_enabled:
            self.set(item_id, 'sync', '❌')
        
        return item_id
    
    def clear_items(self):
        """Clear all items"""
        for item in self.get_children():
            self.delete(item)
        self.items.clear()
    
    def navigate_to(self, path: str):
        """Navigate to path"""
        self.current_path = path
        if self.callbacks.get('navigate'):
            self.callbacks['navigate'](path)
    
    def go_up(self, event=None):
        """Go up one directory"""
        if self.current_path:
            parent_path = str(Path(self.current_path).parent)
            if parent_path != self.current_path:
                self.navigate_to(parent_path)
    
    def refresh(self):
        """Refresh current directory"""
        if self.callbacks.get('refresh'):
            self.callbacks['refresh']()
    
    def get_selected_items(self) -> List[FileItem]:
        """Get selected file items"""
        selected = []
        for item_id in self.selection():
            file_item = self.items.get(item_id)
            if file_item:
                selected.append(file_item)
        return selected
    
    def toggle_sync(self, enabled: Optional[bool]):
        """Toggle sync for selected items"""
        selected_items = self.get_selected_items()
        if not selected_items:
            return
        
        for file_item in selected_items:
            if enabled is None:
                file_item.sync_enabled = not file_item.sync_enabled
            else:
                file_item.sync_enabled = enabled
        
        # Update display
        for item_id, file_item in self.items.items():
            if file_item in selected_items:
                sync_text = "✅" if file_item.sync_enabled else "❌"
                self.set(item_id, 'sync', sync_text)
        
        # Notify callback
        if self.callbacks.get('sync_changed'):
            self.callbacks['sync_changed'](selected_items)
    
    # Placeholder methods for context menu actions
    def upload_selected(self): pass
    def download_selected(self): pass
    def open_in_finder(self): pass
    def view_remote_file(self): pass
    def rename_item(self): pass
    def delete_item(self): pass
    def copy_path(self): pass
    def show_properties(self): pass
    
    def set_callback(self, event: str, callback: Callable):
        """Set callback for events"""
        self.callbacks[event] = callback


class DualPaneFileManager:
    """Modern dual-pane file manager similar to Transmit"""

    def __init__(self, parent, site_manager):
        self.parent = parent
        self.site_manager = site_manager
        self.window = None
        self.local_tree = None
        self.remote_tree = None
        self.current_site = None
        self.transfer_manager = None
        self.site_combo = None
        self.site_var = None
        self._destroyed = False

    def show(self, site_id: str = None):
        """Show file manager window"""
        try:
            if self._destroyed:
                # Reset if previously destroyed
                self._destroyed = False
                self.window = None

            if self.window is None or not self.window.winfo_exists():
                self.create_window()

            if site_id:
                self.current_site = site_id
                self.load_site_data()

            if self.window and self.window.winfo_exists():
                self.window.deiconify()
                self.window.lift()
        except tk.TclError as e:
            # Window was destroyed, recreate it
            print(f"Window error, recreating: {e}")
            self.window = None
            self._destroyed = False
            self.create_window()
            if site_id:
                self.current_site = site_id
                self.load_site_data()
            if self.window:
                self.window.deiconify()
                self.window.lift()

    def create_window(self):
        """Create file manager window"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("🗂️ FTP AutoSync File Manager")
        self.window.geometry("1400x800")
        self.window.transient(self.parent)

        # Handle window destruction
        self.window.protocol("WM_DELETE_WINDOW", self.on_window_close)

        self.create_widgets()
        self.setup_transfer_manager()

    def on_window_close(self):
        """Handle window close event"""
        try:
            self._destroyed = True
            if self.window:
                self.window.destroy()
            self.window = None
            self.site_combo = None
            self.site_var = None
        except:
            pass

    def create_widgets(self):
        """Create window widgets"""
        # Main container
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Toolbar
        self.create_toolbar(main_frame)

        # Dual pane container
        panes_frame = ttk.Frame(main_frame)
        panes_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        # Create paned window
        paned_window = ttk.PanedWindow(panes_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)

        # Local pane
        local_frame = self.create_pane(paned_window, "Local Files", "local")
        paned_window.add(local_frame, weight=1)

        # Remote pane
        remote_frame = self.create_pane(paned_window, "Remote Files", "remote")
        paned_window.add(remote_frame, weight=1)

        # Status bar
        self.create_status_bar(main_frame)

    def create_toolbar(self, parent):
        """Create toolbar with navigation and actions"""
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))

        # Site selector
        ttk.Label(toolbar_frame, text="Site:").pack(side=tk.LEFT, padx=(0, 5))

        self.site_var = tk.StringVar()
        self.site_combo = ttk.Combobox(toolbar_frame, textvariable=self.site_var,
                                      state="readonly", width=20)
        self.site_combo.pack(side=tk.LEFT, padx=(0, 20))

        # Bind with error handling
        def safe_site_changed(event):
            try:
                self.on_site_changed(event)
            except Exception as e:
                print(f"Error in combobox event: {e}")

        self.site_combo.bind('<<ComboboxSelected>>', safe_site_changed)

        # Navigation buttons
        nav_frame = ttk.Frame(toolbar_frame)
        nav_frame.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Button(nav_frame, text="🔄", command=self.refresh_both,
                  width=3).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(nav_frame, text="🏠", command=self.go_home,
                  width=3).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(nav_frame, text="⬆️", command=self.go_up,
                  width=3).pack(side=tk.LEFT, padx=(0, 2))

        # Transfer buttons
        transfer_frame = ttk.Frame(toolbar_frame)
        transfer_frame.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Button(transfer_frame, text="📤 Upload", command=self.upload_selected,
                  style="Accent.TButton").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(transfer_frame, text="📥 Download", command=self.download_selected,
                  style="Accent.TButton").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(transfer_frame, text="🔄 Sync", command=self.sync_selected,
                  style="Accent.TButton").pack(side=tk.LEFT, padx=(0, 5))

        # View options
        view_frame = ttk.Frame(toolbar_frame)
        view_frame.pack(side=tk.RIGHT)

        ttk.Button(view_frame, text="🚀 Transfers", command=self.show_transfers,
                  width=12).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(view_frame, text="⚙️ Settings", command=self.show_settings,
                  width=12).pack(side=tk.RIGHT, padx=(5, 0))

    def create_pane(self, parent, title: str, side: str):
        """Create a file browser pane"""
        # Pane frame
        pane_frame = ttk.LabelFrame(parent, text=f"📁 {title}", padding="5")

        # Path bar
        path_frame = ttk.Frame(pane_frame)
        path_frame.pack(fill=tk.X, pady=(0, 5))

        path_var = tk.StringVar()
        path_entry = ttk.Entry(path_frame, textvariable=path_var, font=('Monaco', 10))
        path_entry.pack(fill=tk.X)

        # File tree
        tree = ModernFileTree(pane_frame, side=side, selectmode='extended')
        tree.pack(fill=tk.BOTH, expand=True)

        # Store references
        if side == "local":
            self.local_tree = tree
            self.local_path_var = path_var
        else:
            self.remote_tree = tree
            self.remote_path_var = path_var

        # Setup callbacks
        tree.set_callback('navigate', lambda path: self.navigate_pane(side, path))
        tree.set_callback('refresh', lambda: self.refresh_pane(side))
        tree.set_callback('sync_changed', lambda items: self.on_sync_changed(side, items))

        return pane_frame

    def create_status_bar(self, parent):
        """Create status bar"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, pady=(10, 0))

        # Left side - status
        self.status_label = ttk.Label(status_frame, text="Ready")
        self.status_label.pack(side=tk.LEFT)

        # Right side - connection info
        self.connection_label = ttk.Label(status_frame, text="Not connected")
        self.connection_label.pack(side=tk.RIGHT)

    def setup_transfer_manager(self):
        """Setup transfer manager"""
        from .transfer_manager import TransferManagerWindow
        self.transfer_manager = TransferManagerWindow(self.window)

    def load_site_data(self):
        """Load data for current site"""
        try:
            if self._destroyed or not self.current_site:
                return

            site = self.site_manager.get_site(self.current_site)
            if not site:
                return

            # Update site combo only if it exists
            if self.site_combo and self.site_var:
                sites = self.site_manager.get_all_sites()
                site_names = [s['name'] for s in sites]
                self.site_combo['values'] = site_names
                self.site_var.set(site['name'])

            # Load local directory
            local_path = site['config']['local']['watch_path']
            self.load_local_directory(local_path)

            # Connect and load remote directory
            self.connect_and_load_remote(site)
        except tk.TclError:
            # Widget was destroyed
            pass
        except Exception as e:
            print(f"Error loading site data: {e}")

    def load_local_directory(self, path: str):
        """Load local directory"""
        try:
            from pathlib import Path
            path_obj = Path(path).expanduser().resolve()

            self.local_tree.clear_items()
            self.local_path_var.set(str(path_obj))

            # Add parent directory entry
            if path_obj.parent != path_obj:
                parent_item = FileItem("..", str(path_obj.parent), True)
                parent_item.name = "⬆️ .."
                self.local_tree.add_file_item(parent_item)

            # Add directories first
            for item in sorted(path_obj.iterdir()):
                if item.is_dir():
                    file_item = FileItem(
                        item.name, str(item), True,
                        modified=datetime.fromtimestamp(item.stat().st_mtime)
                    )
                    self.local_tree.add_file_item(file_item)

            # Add files
            for item in sorted(path_obj.iterdir()):
                if item.is_file():
                    stat = item.stat()
                    file_item = FileItem(
                        item.name, str(item), False,
                        size=stat.st_size,
                        modified=datetime.fromtimestamp(stat.st_mtime)
                    )
                    self.local_tree.add_file_item(file_item)

            self.status_label.config(text=f"Loaded {len(list(path_obj.iterdir()))} items from local directory")

        except Exception as e:
            self.status_label.config(text=f"Error loading local directory: {e}")

    def connect_and_load_remote(self, site):
        """Connect to FTP and load remote directory"""
        def connect_thread():
            try:
                # This would connect to FTP and load remote files
                # For now, just show a placeholder
                self.window.after(0, lambda: self.load_remote_placeholder())
            except Exception as e:
                self.window.after(0, lambda: self.status_label.config(text=f"Connection failed: {e}"))

        threading.Thread(target=connect_thread, daemon=True).start()
        self.status_label.config(text="Connecting to FTP server...")

    def load_remote_placeholder(self):
        """Load remote directory placeholder"""
        self.remote_tree.clear_items()
        self.remote_path_var.set("/")

        # Add some placeholder items
        placeholder_items = [
            FileItem("index.php", "/index.php", False, 2048),
            FileItem("config.php", "/config.php", False, 1024),
            FileItem("assets", "/assets", True),
            FileItem("includes", "/includes", True),
        ]

        for item in placeholder_items:
            self.remote_tree.add_file_item(item)

        self.status_label.config(text="Connected to FTP server")
        self.connection_label.config(text="🟢 Connected")

    # Event handlers
    def on_site_changed(self, event=None):
        """Handle site selection change"""
        try:
            if self._destroyed or not self.site_var:
                return

            site_name = self.site_var.get()
            sites = self.site_manager.get_all_sites()
            for site in sites:
                if site['name'] == site_name:
                    self.current_site = site['id']
                    self.load_site_data()
                    break
        except tk.TclError:
            # Widget was destroyed
            pass
        except Exception as e:
            print(f"Error in site change handler: {e}")

    def navigate_pane(self, side: str, path: str):
        """Navigate pane to path"""
        if side == "local":
            self.load_local_directory(path)
        else:
            # Load remote directory
            pass

    def refresh_pane(self, side: str):
        """Refresh pane"""
        if side == "local":
            current_path = self.local_path_var.get()
            self.load_local_directory(current_path)
        else:
            # Refresh remote
            pass

    def on_sync_changed(self, side: str, items: List[FileItem]):
        """Handle sync setting changes"""
        self.status_label.config(text=f"Updated sync settings for {len(items)} items")

    # Toolbar actions
    def refresh_both(self):
        """Refresh both panes"""
        self.refresh_pane("local")
        self.refresh_pane("remote")

    def go_home(self):
        """Go to home directories"""
        import os
        self.load_local_directory(os.path.expanduser("~"))

    def go_up(self):
        """Go up one directory in both panes"""
        self.local_tree.go_up()
        self.remote_tree.go_up()

    def upload_selected(self):
        """Upload selected local files"""
        selected = self.local_tree.get_selected_items()
        if selected:
            self.status_label.config(text=f"Starting upload of {len(selected)} items...")
            self.show_transfers()

    def download_selected(self):
        """Download selected remote files"""
        selected = self.remote_tree.get_selected_items()
        if selected:
            self.status_label.config(text=f"Starting download of {len(selected)} items...")
            self.show_transfers()

    def sync_selected(self):
        """Sync selected items"""
        local_selected = self.local_tree.get_selected_items()
        remote_selected = self.remote_tree.get_selected_items()
        total = len(local_selected) + len(remote_selected)
        if total > 0:
            self.status_label.config(text=f"Starting sync of {total} items...")
            self.show_transfers()

    def show_transfers(self):
        """Show transfer manager"""
        if self.transfer_manager:
            self.transfer_manager.show()

    def show_settings(self):
        """Show settings dialog"""
        self.status_label.config(text="Settings dialog would open here")
